import '../Extensions/pattern_type.dart';

/// Validates an email address format and checks for duplicates.
///
/// This function performs comprehensive email validation including format checking
/// using RFC-compliant regex patterns and optional duplicate detection against
/// a list of existing emails.
///
/// Returns `null` if the email is valid, or a descriptive error message if invalid.
///
/// ## Validation Process
///
/// 1. **Whitespace handling**: Optionally trims leading/trailing whitespace
/// 2. **Empty check**: Validates that the email is not empty
/// 3. **Format validation**: Uses regex to ensure proper email format
/// 4. **Duplicate check**: Optionally checks against existing emails list
///
/// ## Parameters
///
/// * [email]: The email address to validate (required)
/// * [emptyMessage]: Custom error message for empty email.
///   Default: "Please enter your email."
/// * [invalidFormatMessage]: Custom error message for invalid format.
///   Default: "The email address is badly formatted."
/// * [trimWhitespace]: Whether to trim whitespace before validation. Default: `true`
/// * [existingEmails]: Optional list of existing emails to check against
/// * [alreadyExistsMessage]: Custom error message for duplicate emails.
///   Default: "This email is already registered."
///
/// ## Example
///
/// ```dart
/// String? error = validateEmail(
///   email: "<EMAIL>",
///   existingEmails: ["<EMAIL>"],
///   alreadyExistsMessage: "Email already taken",
/// );
/// ```
String? validateEmail({
  required String email,
  String? emptyMessage,
  String? invalidFormatMessage,
  bool trimWhitespace = true,
  List<String>? existingEmails,
  String? alreadyExistsMessage,
}) {
  final String input = trimWhitespace ? email.trim() : email;

  if (input.isEmpty) {
    return emptyMessage ?? "Please enter your email.";
  }

  if (!PatternType.email.isValid(input)) {
    return invalidFormatMessage ?? "The email address is badly formatted.";
  }

  if (existingEmails != null && existingEmails.contains(input)) {
    return alreadyExistsMessage ?? "This email is already registered.";
  }

  return null;
}
