## 0.0.2

### ✨ New Features
* **Address Validation**: Added comprehensive address validation with component requirements (street number, street name, city, country)
* **URL Validation**: Added asynchronous URL validation with multiple return types (`String`, `bool`, `UrlValidationResult`)
* **Advanced URL Features**: Domain whitelisting, scheme restrictions, relative URL support, and optional DNS verification

### 🚀 Major Improvements
* **Enhanced Documentation**: Comprehensive README.md with detailed usage examples and best practices
* **Navigation Features**: Added table of contents and direct links to code implementations
* **Improved Code Documentation**: Streamlined dartdoc comments while maintaining pub.dev compliance
* **Better Example App**: Complete rewrite with practical demonstrations of all validation types

### 📚 Documentation
* Added comprehensive usage guide for all validation types including new address and URL validation
* Added "View Implementation" links that jump directly to code
* Added "View Tests" links for each validation method
* Improved form integration examples with complete code samples
* Added best practices section with performance considerations
* Added table of contents with clickable navigation

### 🧪 Testing
* **Comprehensive test coverage**: Added complete test suites for address and URL validation
* **71 total tests**: All validation methods now have extensive test coverage including edge cases
* Added tests for trimWhitespace functionality across all validators
* Added tests for common parameters behavior
* Added URL validation tests for different return types and advanced features

### 🎯 Developer Experience
* **Enhanced Example App**: Complete rewrite with better UI, clear sections, and practical examples
* Clickable table of contents for easy navigation
* Direct links from documentation to implementation code
* Improved error messages and validation feedback
* Better form integration patterns

### 🔧 Code Quality
* Simplified and focused dartdoc comments (reduced from verbose to concise while maintaining clarity)
* Maintained pub.dev quality standards (20%+ documentation coverage)
* Consistent code formatting and linting compliance
* Better separation between user-facing (README) and developer documentation (code comments)
* Added proper documentation for all new classes and methods

## 0.0.1
* initial release.
