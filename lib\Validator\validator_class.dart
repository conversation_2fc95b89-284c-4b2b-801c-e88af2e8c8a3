import '../Extensions/pattern_type.dart';
import 'adress_validate.dart';
import 'email_validate.dart';
import 'phone_validate.dart';
import 'password_validate.dart';
import 'custom_validate.dart';
import 'url_validate.dart';

/// A comprehensive validation library for Flutter applications.
///
/// [SahihValidator] provides static methods for validating common input types
/// including emails, phone numbers, passwords, addresses, URLs, and custom fields.
/// All validation methods return `null` for valid input or a `String` error message
/// for invalid input, making them perfect for use with Flutter's form validation.
///
/// ## Common Parameters
///
/// Many validation methods share these common parameters:
///
/// * [trimWhitespace]: When `true` (default), removes leading and trailing
///   whitespace from the input before validation. This helps handle user input
///   that may accidentally include spaces.
///
/// * [emptyMessage]: Custom error message to display when the input is empty.
///   If not provided, a default message will be used.
///
/// * [existingValues]: A list of values that already exist (e.g., registered
///   emails). If the input matches any of these values, validation will fail.
///
/// * [alreadyExistsMessage]: Custom error message when the input matches an
///   existing value. If not provided, a default message will be used.
///
/// ## Example Usage
///
/// ```dart
/// // Email validation
/// String? emailError = SahihValidator.email(
///   email: "<EMAIL>",
///   emptyMessage: "Email is required",
///   invalidFormatMessage: "Please enter a valid email",
/// );
///
/// // Phone validation
/// String? phoneError = SahihValidator.phone(
///   phone: "+1234567890",
///   trimWhitespace: true,
/// );
///
/// // Custom validation
/// String? nameError = SahihValidator.custom(
///   value: "John Doe",
///   title: "Name",
///   minLength: 2,
///   maxLength: 50,
/// );
/// ```
/// A collection of static validation methods for common input types.
///
/// This class provides validation for:
/// - Email addresses
/// - Phone numbers
/// - Passwords
/// - Addresses
/// - URLs
/// - Custom validation rules
/// A comprehensive validator for common input types in Flutter applications.
///
/// Provides static methods for validating:
/// - Email addresses
/// - Phone numbers
/// - Passwords
/// - Addresses
/// - URLs
/// - Custom input patterns
///
/// All methods return `null` if validation passes, or a String error message
/// if validation fails.
class SahihValidator {
  /// Validates an email address format and checks against existing emails.
  ///
  /// Returns `null` if the email is valid, or an error message string if invalid.
  ///
  /// The validation checks:
  /// 1. Whether the email is empty (if required)
  /// 2. Whether the email format is valid using RFC-compliant regex
  /// 3. Whether the email already exists in the provided list
  ///
  /// ## Parameters
  ///
  /// * [email]: The email address to validate (required)
  /// * [emptyMessage]: Custom message when email is empty.
  ///   Default: "Please enter your email."
  /// * [invalidFormatMessage]: Custom message for invalid email format.
  ///   Default: "The email address is badly formatted."
  /// * [trimWhitespace]: Whether to trim whitespace before validation.
  ///   Default: `true`
  /// * [existingEmails]: List of emails that already exist. If provided,
  ///   validation fails if the input email matches any in this list.
  /// * [alreadyExistsMessage]: Custom message when email already exists.
  ///   Default: "This email is already registered."
  ///
  /// ## Example
  ///
  /// ```dart
  /// String? error = SahihValidator.email(
  ///   email: "<EMAIL>",
  ///   existingEmails: ["<EMAIL>", "<EMAIL>"],
  ///   alreadyExistsMessage: "This email is already taken",
  /// );
  ///
  /// if (error != null) {
  ///   print("Email validation failed: $error");
  /// }
  /// ```
  /// Validates an email address against standard format requirements.
  ///
  /// Parameters:
  /// - [email]: The email address to validate
  /// - [emptyMessage]: Custom message for empty input (default: 'Please enter your email.')
  /// - [invalidFormatMessage]: Custom message for invalid format (default: 'The email address is badly formatted.')
  /// - [trimWhitespace]: Whether to trim whitespace from input (default: true)
  /// - [existingEmails]: List of existing emails to check against for uniqueness
  /// - [alreadyExistsMessage]: Custom message for duplicate emails (default: 'This email is already registered.')
  ///
  /// Returns `null` if valid, or an error message if invalid.
  ///
  /// Example:
  /// ```dart
  /// final error = SahihValidator.email(
  ///   email: '<EMAIL>',
  ///   existingEmails: ['<EMAIL>'],
  /// );
  /// if (error != null) {
  ///   print('Validation error: $error');
  /// }
  /// ```
  static String? email({
    required String email,
    String? emptyMessage,
    String? invalidFormatMessage,
    bool trimWhitespace = true,
    List<String>? existingEmails,
    String? alreadyExistsMessage,
  }) {
    return validateEmail(
      email: email,
      emptyMessage: emptyMessage,
      invalidFormatMessage: invalidFormatMessage,
      trimWhitespace: trimWhitespace,
      existingEmails: existingEmails,
      alreadyExistsMessage: alreadyExistsMessage,
    );
  }

  /// Validates a phone number format and checks against existing phone numbers.
  ///
  /// Returns `null` if the phone number is valid, or an error message string if invalid.
  ///
  /// The validation accepts phone numbers with 10-15 digits, optionally starting
  /// with a '+' for country codes. Common formats like dashes, spaces, or
  /// parentheses are not supported and will cause validation to fail.
  ///
  /// ## Parameters
  ///
  /// * [phone]: The phone number to validate (required)
  /// * [emptyMessage]: Custom message when phone is empty.
  ///   Default: "Please enter your phone number."
  /// * [invalidFormatMessage]: Custom message for invalid phone format.
  ///   Default: "The phone number is invalid. Please enter a valid 10–15 digit number."
  /// * [trimWhitespace]: Whether to trim whitespace before validation.
  ///   Default: `true`
  /// * [existingPhones]: List of phone numbers that already exist.
  /// * [alreadyExistsMessage]: Custom message when phone already exists.
  ///   Default: "This phone number is already registered."
  ///
  /// ## Valid Examples
  /// * "1234567890" (10 digits)
  /// * "+1234567890" (with country code)
  /// * "123456789012345" (15 digits maximum)
  ///
  /// ## Invalid Examples
  /// * "************" (contains dashes)
  /// * "(*************" (contains spaces and parentheses)
  /// * "123" (too short)
  /// * "12345678901234567890" (too long)
  /// Validates a phone number against basic format requirements.
  ///
  /// Accepts numbers with optional '+' prefix (10-15 digits total).
  ///
  /// Parameters:
  /// - [phone]: The phone number to validate
  /// - [emptyMessage]: Custom message for empty input (default: 'Please enter your phone number.')
  /// - [invalidFormatMessage]: Custom message for invalid format (default: 'The phone number is invalid...')
  /// - [trimWhitespace]: Whether to trim whitespace from input (default: true)
  /// - [existingPhones]: List of existing numbers to check against
  /// - [alreadyExistsMessage]: Custom message for duplicates (default: 'This phone number is already registered.')
  ///
  /// Returns `null` if valid, or an error message if invalid.
  ///
  /// Example:
  /// ```dart
  /// final error = SahihValidator.phone(
  ///   phone: '+1234567890',
  ///   trimWhitespace: false, // Preserve formatting
  /// );
  /// ```
  static String? phone({
    required String phone,
    String? emptyMessage,
    String? invalidFormatMessage,
    bool trimWhitespace = true,
    List<String>? existingPhones,
    String? alreadyExistsMessage,
  }) {
    return validatePhone(
      phone: phone,
      emptyMessage: emptyMessage,
      invalidFormatMessage: invalidFormatMessage,
      trimWhitespace: trimWhitespace,
      existingPhones: existingPhones,
      alreadyExistsMessage: alreadyExistsMessage,
    );
  }

  /// Validates a login password by checking if it's not empty.
  ///
  /// This is a simple validation method intended for login forms where you
  /// only need to ensure the user has entered a password. For password
  /// strength validation during registration, use [passwordParts] instead.
  ///
  /// Returns `null` if the password is not empty, or an error message if empty.
  ///
  /// ## Parameters
  ///
  /// * [password]: The password to validate (required)
  /// * [emptyMessage]: Custom message when password is empty.
  ///   Default: "Please enter your password."
  ///
  /// ## Example
  ///
  /// ```dart
  /// String? error = SahihValidator.loginPassword(
  ///   password: userInput,
  ///   emptyMessage: "Password is required to login",
  /// );
  /// ```
  /// Validates that a password is not empty (basic login validation).
  ///
  /// Parameters:
  /// - [password]: The password to validate
  /// - [emptyMessage]: Custom message for empty input (default: 'Please enter your password.')
  ///
  /// Returns `null` if valid (non-empty), or an error message if invalid.
  ///
  /// Example:
  /// ```dart
  /// final error = SahihValidator.loginPassword(password: input);
  /// ```
  static String? loginPassword({
    required String password,
    String? emptyMessage,
  }) {
    return validateLoginPassword(
      password: password,
      emptyMessage: emptyMessage,
    );
  }

  /// Validates password strength for new password creation.
  ///
  /// This method performs comprehensive password strength validation including
  /// length requirements, character diversity, entropy calculation, and checks
  /// against common passwords. It returns `null` for strong passwords or a
  /// detailed error message listing all validation failures.
  ///
  /// ## Validation Rules
  ///
  /// * Minimum 8 characters
  /// * At least one uppercase letter (A-Z)
  /// * At least one lowercase letter (a-z)
  /// * At least one digit (0-9)
  /// * Cannot be only numbers
  /// * Cannot be a common/weak password
  /// * Must have sufficient entropy (complexity)
  ///
  /// ## Parameters
  ///
  /// * [input]: The password to validate (required)
  /// * [commonPasswords]: Optional list of passwords to reject. If not provided,
  ///   uses a built-in list of common weak passwords like "password", "123456", etc.
  ///
  /// ## Example
  ///
  /// ```dart
  /// String? error = SahihValidator.passwordParts(
  ///   "MyNewPassword123!",
  ///   commonPasswords: ["companyname", "oldpassword"],
  /// );
  ///
  /// if (error != null) {
  ///   // error contains detailed feedback like:
  ///   // "One uppercase letter required.\nOne digit required."
  ///   print("Password requirements: $error");
  /// }
  /// ```
  /// Validates password strength with multiple requirements.
  ///
  /// Checks for:
  /// - Minimum 8 characters
  /// - At least one uppercase letter
  /// - At least one lowercase letter
  /// - At least one digit
  /// - Not consisting of only numbers
  /// - Not in common passwords list
  ///
  /// Parameters:
  /// - [input]: The password to validate
  /// - [commonPasswords]: Optional list of banned/common passwords
  ///
  /// Returns concatenated error messages for all failed checks, or `null` if valid.
  ///
  /// Example:
  /// ```dart
  /// final error = SahihValidator.passwordParts(
  ///   'weak',
  ///   commonPasswords: ['password', '123456']
  /// );
  /// ```
  static String? passwordParts(String input, {List<String>? commonPasswords}) {
    return validatePasswordParts(
      input: input,
      commonPasswords: commonPasswords,
    );
  }

  /// Provides flexible custom validation with multiple configurable rules.
  ///
  /// This is the most versatile validation method, allowing you to combine
  /// multiple validation rules for any type of input field. It's perfect for
  /// validating names, usernames, product codes, or any custom field requirements.
  ///
  /// Returns `null` if the value passes all validation rules, or an error message
  /// describing the first validation failure encountered.
  ///
  /// ## Validation Rules (applied in order)
  ///
  /// 1. **Required check**: If [isRequired] is true and value is empty
  /// 2. **Existing values check**: If value exists in [existingValues] list
  /// 3. **Length validation**: [minLength] and [maxLength] constraints
  /// 4. **Character restrictions**: [allowOnlyNumbers] or [allowOnlyLetters]
  /// 5. **Pattern matching**: Custom [pattern] regex or [patternType] enum
  /// 6. **Custom function**: User-defined [customValidator] function
  ///
  /// ## Parameters
  ///
  /// * [value]: The input value to validate (required)
  /// * [title]: Field name used in default error messages. Default: "Field"
  /// * [trimWhitespace]: Whether to trim whitespace before validation. Default: `true`
  /// * [isRequired]: Whether the field is required. Default: `true`
  /// * [minLength]: Minimum character length (optional)
  /// * [maxLength]: Maximum character length (optional)
  /// * [pattern]: Custom regex pattern string (optional)
  /// * [patternType]: Predefined pattern from [PatternType] enum (optional)
  /// * [allowOnlyNumbers]: If true, only numeric characters allowed. Default: `false`
  /// * [allowOnlyLetters]: If true, only letter characters allowed. Default: `false`
  /// * [customValidator]: Custom validation function that takes the value and
  ///   returns `null` for valid or error message for invalid
  /// * [existingValues]: List of values that already exist
  /// * Various message parameters for customizing error messages
  ///
  /// ## Example
  ///
  /// ```dart
  /// // Username validation
  /// String? error = SahihValidator.custom(
  ///   value: "john_doe123",
  ///   title: "Username",
  ///   minLength: 3,
  ///   maxLength: 20,
  ///   pattern: r'^[a-zA-Z0-9_]+$', // alphanumeric and underscore only
  ///   existingValues: ["admin", "root", "john_doe123"],
  ///   customValidator: (value) {
  ///     if (value.startsWith('_')) return "Username cannot start with underscore";
  ///     return null;
  ///   },
  /// );
  /// ```
  static String? custom({
    required String value,
    String? title,
    bool trimWhitespace = true,
    bool isRequired = true,
    int? minLength,
    int? maxLength,
    String? pattern,
    PatternType? patternType,
    bool allowOnlyNumbers = false,
    bool allowOnlyLetters = false,
    String? emptyMessage,
    String? minLengthMessage,
    String? maxLengthMessage,
    String? invalidPatternMessage,
    String? invalidNumberMessage,
    String? invalidLettersMessage,
    String? Function(String value)? customValidator,
    List<String>? existingValues,
    String? alreadyExistsMessage,
  }) {
    return customValidate(
      value: value,
      title: title,
      trimWhitespace: trimWhitespace,
      isRequired: isRequired,
      minLength: minLength,
      maxLength: maxLength,
      pattern: pattern,
      patternType: patternType,
      allowOnlyNumbers: allowOnlyNumbers,
      allowOnlyLetters: allowOnlyLetters,
      emptyMessage: emptyMessage,
      minLengthMessage: minLengthMessage,
      maxLengthMessage: maxLengthMessage,
      invalidPatternMessage: invalidPatternMessage,
      invalidNumberMessage: invalidNumberMessage,
      invalidLettersMessage: invalidLettersMessage,
      customValidator: customValidator,
      existingValues: existingValues,
      alreadyExistsMessage: alreadyExistsMessage,
    );
  }

  /// Validates physical addresses with comprehensive formatting and content rules.
  ///
  /// This method provides detailed address validation including length constraints,
  /// component requirements (street number, street name, city, country), character
  /// type restrictions, and content filtering. It's designed to handle various
  /// international address formats while ensuring data quality.
  ///
  /// Returns `null` if the address is valid, or an error message describing
  /// the first validation failure encountered.
  ///
  /// ## Validation Features
  ///
  /// * **Length validation**: Configurable minimum and maximum character limits
  /// * **Component detection**: Can require street numbers, street names, cities, countries
  /// * **Character filtering**: Control whether numbers, letters, or special characters are allowed
  /// * **Content filtering**: Block forbidden words or require specific words
  /// * **Duplicate detection**: Check against existing addresses
  ///
  /// ## Parameters
  ///
  /// * [address]: The address string to validate (required)
  /// * [title]: Field name for error messages. Default: "Address"
  /// * [trimWhitespace]: Whether to trim whitespace. Default: `true`
  /// * [isRequired]: Whether the field is required. Default: `true`
  /// * [minLength]: Minimum character length. Default: 10
  /// * [maxLength]: Maximum character length. Default: 200
  /// * [requireStreetNumber]: Whether a street number is required. Default: `false`
  /// * [requireStreetName]: Whether a street name is required. Default: `true`
  /// * [requireCity]: Whether a city name is required. Default: `false`
  /// * [requireCountry]: Whether a country is required. Default: `false`
  /// * [allowNumbers], [allowLetters], [allowSpecialChars]: Character type controls
  /// * [forbiddenWords]: List of words that are not allowed in the address
  /// * [requiredWords]: List of words that must be present in the address
  /// * [existingAddresses]: List of addresses that already exist
  /// * Various message parameters for customizing error messages
  ///
  /// ## Example
  ///
  /// ```dart
  /// String? error = SahihValidator.address(
  ///   address: "123 Main Street, Springfield, USA",
  ///   requireStreetNumber: true,
  ///   requireStreetName: true,
  ///   requireCity: true,
  ///   requireCountry: true,
  ///   forbiddenWords: ["PO Box"], // Don't allow P.O. boxes
  ///   minLength: 15,
  /// );
  /// ```
  static String? address({
    required String address,
    String? title = "Address",
    String? emptyMessage,
    bool trimWhitespace = true,
    List<String>? existingAddresses,
    String? alreadyExistsMessage,
    bool isRequired = true,
    int minLength = 10,
    int maxLength = 200,
    bool requireStreetNumber = false,
    bool requireStreetName = true,
    bool requireCity = false,
    bool requireCountry = false,
    bool allowSpecialChars = true,
    bool allowNumbers = true,
    bool allowLetters = true,
    List<String>? forbiddenWords,
    List<String>? requiredWords,
    String? minLengthMessage,
    String? maxLengthMessage,
    String? missingStreetNumberMessage,
    String? missingStreetNameMessage,
    String? missingCityMessage,
    String? missingCountryMessage,
    String? invalidSpecialCharsMessage,
    String? invalidNumbersMessage,
    String? invalidLettersMessage,
    String? forbiddenWordsMessage,
    String? requiredWordsMessage,
  }) {
    return validateAddress(
      address: address,
      emptyMessage: emptyMessage,
      trimWhitespace: trimWhitespace,
      existingAddresses: existingAddresses,
      alreadyExistsMessage: alreadyExistsMessage,
      isRequired: isRequired,
      minLength: minLength,
      maxLength: maxLength,
      requireStreetNumber: requireStreetNumber,
      requireStreetName: requireStreetName,
      requireCity: requireCity,
      requireCountry: requireCountry,
      allowSpecialChars: allowSpecialChars,
      allowNumbers: allowNumbers,
      allowLetters: allowLetters,
      forbiddenWords: forbiddenWords,
      requiredWords: requiredWords,
      minLengthMessage: minLengthMessage,
      maxLengthMessage: maxLengthMessage,
      missingStreetNumberMessage: missingStreetNumberMessage,
      missingStreetNameMessage: missingStreetNameMessage,
      missingCityMessage: missingCityMessage,
      missingCountryMessage: missingCountryMessage,
      invalidSpecialCharsMessage: invalidSpecialCharsMessage,
      invalidNumbersMessage: invalidNumbersMessage,
      invalidLettersMessage: invalidLettersMessage,
      forbiddenWordsMessage: forbiddenWordsMessage,
      requiredWordsMessage: requiredWordsMessage,
      title: title,
    );
  }

  /// Asynchronously validates URLs with comprehensive format and domain checking.
  ///
  /// This method provides advanced URL validation including format verification,
  /// scheme restrictions, domain whitelisting, and optional DNS lookup to verify
  /// domain existence. The return type is generic and can be customized based on
  /// your needs.
  ///
  /// Returns different types based on the generic parameter [T]:
  /// * `String`: Returns the validation message
  /// * `bool`: Returns true for valid URLs, false for invalid
  /// * `UrlValidationResult`: Returns complete validation result with parsed URL data
  ///
  /// ## Validation Features
  ///
  /// * **Format validation**: Ensures proper URL structure and syntax
  /// * **Scheme filtering**: Restrict to specific protocols (http, https, ftp, etc.)
  /// * **Domain whitelisting**: Only allow specific domains
  /// * **Relative URL support**: Optionally allow relative paths
  /// * **DNS verification**: Optionally verify domain exists via DNS lookup
  /// * **URL normalization**: Clean up and standardize URL format
  ///
  /// ## Parameters
  ///
  /// * [url]: The URL string to validate (required)
  /// * [allowRelative]: Whether to allow relative URLs like "/path/to/resource". Default: `false`
  /// * [allowedSchemes]: List of allowed URL schemes. If provided, only these schemes are valid.
  ///   Example: `["http", "https"]`
  /// * [allowedDomains]: List of allowed domains. If provided, only these domains are valid.
  ///   Example: `["example.com", "mysite.org"]`
  /// * [normalize]: Whether to normalize the URL (remove trailing slashes, etc.). Default: `true`
  /// * [checkDomainExists]: Whether to perform DNS lookup to verify domain exists. Default: `false`
  ///   **Warning**: This makes a network request and can be slow.
  ///
  /// ## Example Usage
  ///
  /// ```dart
  /// // Get validation message
  /// String message = await SahihValidator.urlAsync<String>("https://example.com");
  ///
  /// // Get boolean result
  /// bool isValid = await SahihValidator.urlAsync<bool>("https://example.com");
  ///
  /// // Get complete result with parsed data
  /// UrlValidationResult result = await SahihValidator.urlAsync("https://example.com/path?q=1");
  /// if (result.isValid) {
  ///   print("Domain: ${result.data?.domain}");
  ///   print("Path: ${result.data?.path}");
  /// }
  ///
  /// // Restrict to specific domains
  /// bool isValid = await SahihValidator.urlAsync<bool>(
  ///   "https://example.com",
  ///   allowedDomains: ["example.com", "trusted-site.org"],
  /// );
  /// ```
  static Future<T> urlAsync<T>(
    String url, {
    bool allowRelative = false,
    List<String>? allowedSchemes,
    List<String>? allowedDomains,
    bool normalize = true,
    bool checkDomainExists = false,
  }) async {
    return await isValidUrlAsync<T>(
      url,
      allowRelative: allowRelative,
      allowedSchemes: allowedSchemes,
      allowedDomains: allowedDomains,
      normalize: normalize,
      checkDomainExists: checkDomainExists,
    );
  }
}
