import 'dart:math';

import '../Extensions/password_validate_extension.dart';

/// Validates a password for login purposes by checking if it's not empty.
///
/// This is a simple validation function designed for login forms where you only
/// need to ensure the user has entered a password. For comprehensive password
/// strength validation during registration, use [validatePasswordParts] instead.
///
/// Returns `null` if the password is not empty, or an error message if empty.
///
/// ## Parameters
///
/// * [password]: The password to validate (required)
/// * [emptyMessage]: Custom error message for empty password.
///   Default: "Please enter your password."
///
/// ## Example
///
/// ```dart
/// String? error = validateLoginPassword(
///   password: userInput,
///   emptyMessage: "Password is required to continue",
/// );
/// ```
String? validateLoginPassword({
  required String password,
  String? emptyMessage,
}) {
  if (password.isEmpty) {
    return emptyMessage ?? "Please enter your password.";
  }
  return null;
}

/// Validates password strength with comprehensive security requirements.
///
/// This function performs detailed password strength analysis including character
/// diversity requirements, length validation, entropy calculation, and checks
/// against common weak passwords. It's designed for password creation scenarios
/// where security is paramount.
///
/// Returns `null` for strong passwords, or a detailed error message listing all
/// validation failures. Multiple errors are combined with newline separators.
///
/// ## Security Requirements
///
/// * **Minimum length**: At least 8 characters
/// * **Character diversity**: Must include:
///   - At least one uppercase letter (A-Z)
///   - At least one lowercase letter (a-z)
///   - At least one digit (0-9)
/// * **Pattern restrictions**: Cannot be only numbers
/// * **Common password check**: Rejects passwords from weak password list
/// * **Entropy analysis**: Calculates password complexity and rejects weak passwords
///
/// ## Parameters
///
/// * [input]: The password to validate (required)
/// * [commonPasswords]: Optional list of passwords to reject. If not provided,
///   uses a built-in list of common weak passwords including "password", "123456",
///   "qwerty", and many others.
///
/// ## Example
///
/// ```dart
/// String? error = validatePasswordParts(
///   "MySecureP@ssw0rd!",
///   commonPasswords: ["companyname", "oldpassword"],
/// );
///
/// if (error != null) {
///   // Error might contain multiple requirements:
///   // "At least 8 characters required.\nOne uppercase letter required."
///   List<String> requirements = error.split('\n');
///   for (String requirement in requirements) {
///     print("• $requirement");
///   }
/// }
/// ```
String? validatePasswordParts({
  required String input,
  List<String>? commonPasswords,
}) {
  final errors = <String>[];

  final weakList = commonPasswords ?? weakPasswords;

  if (input.isEmpty) {
    errors.add("Password is required.");
    return errors.join('\n');
  }

  if (weakList.contains(input)) {
    errors.add("Password is too common.");
    return errors.join('\n');
  }

  if (input.length < 8) {
    errors.add("At least 8 characters required.");
  }

  if (!RegExp(r'[A-Z]').hasMatch(input)) {
    errors.add("One uppercase letter required.");
  }

  if (!RegExp(r'[a-z]').hasMatch(input)) {
    errors.add("One lowercase letter required.");
  }

  if (!RegExp(r'[0-9]').hasMatch(input)) {
    errors.add("One digit required.");
  }

  if (RegExp(r'^[0-9]+$').hasMatch(input)) {
    errors.add("Password cannot be only numbers.");
  }

  final charsetSize = calculateCharsetSize(input);
  final entropy = input.length * log(charsetSize) / log(2);

  if (entropy < 60) {
    errors.add("Password is too weak. Add more complexity.");
  }

  return errors.isEmpty ? null : errors.join('\n');
}
