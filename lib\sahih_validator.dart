/// # SahihValidator - Comprehensive Flutter Validation Library
///
/// A powerful and flexible validation library for Flutter applications that provides
/// comprehensive validation for common input types including emails, phone numbers,
/// passwords, addresses, URLs, and custom fields.
///
/// ## Features
///
/// * **Email Validation**: RFC-compliant email format validation with duplicate checking
/// * **Phone Validation**: International phone number format validation (10-15 digits)
/// * **Password Validation**: Both simple login validation and comprehensive strength checking
/// * **Address Validation**: Detailed address validation with component requirements
/// * **URL Validation**: Asynchronous URL validation with domain verification options
/// * **Custom Validation**: Flexible validation with multiple configurable rules
/// * **Form Integration**: Perfect integration with Flutter's form validation system
/// * **Internationalization**: Customizable error messages for all validation types
///
/// ## Quick Start
///
/// Add to your `pubspec.yaml`:
/// ```yaml
/// dependencies:
///   sahih_validator: ^0.0.1
/// ```
///
/// Import the package:
/// ```dart
/// import 'package:sahih_validator/sahih_validator.dart';
/// ```
///
/// ## Basic Usage
///
/// ### Email Validation
/// ```dart
/// String? emailError = SahihValidator.email(
///   email: "<EMAIL>",
///   emptyMessage: "Email is required",
///   invalidFormatMessage: "Please enter a valid email",
/// );
/// ```
///
/// ### Phone Validation
/// ```dart
/// String? phoneError = SahihValidator.phone(
///   phone: "+1234567890",
///   trimWhitespace: true,
/// );
/// ```
///
/// ### Password Validation
/// ```dart
/// // For login (simple check)
/// String? loginError = SahihValidator.loginPassword(
///   password: "userpassword",
/// );
///
/// // For registration (strength check)
/// String? strengthError = SahihValidator.passwordParts(
///   "MySecureP@ssw0rd!",
/// );
/// ```
///
/// ### Custom Validation
/// ```dart
/// String? usernameError = SahihValidator.custom(
///   value: "john_doe",
///   title: "Username",
///   minLength: 3,
///   maxLength: 20,
///   pattern: r'^[a-zA-Z0-9_]+$',
/// );
/// ```
///
/// ## Common Parameters
///
/// Many validation methods share these common parameters:
///
/// ### trimWhitespace (bool)
/// **Default**: `true`
///
/// When enabled, removes leading and trailing whitespace from input before validation.
/// This is helpful for handling user input that may accidentally include spaces.
///
/// ```dart
/// // With trimming (default)
/// SahihValidator.email(
///   email: "  <EMAIL>  ", // Becomes "<EMAIL>"
///   trimWhitespace: true,
/// ); // Returns null (valid)
///
/// // Without trimming
/// SahihValidator.email(
///   email: "  <EMAIL>  ", // Stays "  <EMAIL>  "
///   trimWhitespace: false,
/// ); // Returns error (invalid format)
/// ```
///
/// ### existingValues/existingEmails/existingPhones (`List<String>?`)
/// **Default**: `null`
///
/// Optional lists of values that already exist in your system. If the input matches
/// any value in these lists, validation will fail with a duplicate error.
///
/// ```dart
/// SahihValidator.email(
///   email: "<EMAIL>",
///   existingEmails: ["<EMAIL>", "<EMAIL>"],
///   alreadyExistsMessage: "This email is already registered",
/// ); // Returns "This email is already registered"
/// ```
///
/// ### Custom Error Messages
///
/// All validation methods accept custom error message parameters:
///
/// * `emptyMessage`: When the field is empty
/// * `invalidFormatMessage`: When the format is invalid
/// * `alreadyExistsMessage`: When the value already exists
/// * Method-specific messages (e.g., `minLengthMessage`, `maxLengthMessage`)
///
/// ```dart
/// SahihValidator.phone(
///   phone: "",
///   emptyMessage: "Phone number is required",
///   invalidFormatMessage: "Please enter a valid phone number",
/// );
/// ```
///
/// ## Form Integration
///
/// SahihValidator is designed to work seamlessly with Flutter's form validation:
///
/// ```dart
/// TextFormField(
///   validator: (value) => SahihValidator.email(
///     email: value ?? '',
///     emptyMessage: "Email is required",
///   ),
///   decoration: InputDecoration(labelText: "Email"),
/// )
/// ```
///
/// ## Advanced Usage
///
/// ### URL Validation with Different Return Types
/// ```dart
/// // Get validation message
/// String message = await SahihValidator.urlAsync<String>("https://example.com");
///
/// // Get boolean result
/// bool isValid = await SahihValidator.urlAsync<bool>("https://example.com");
///
/// // Get complete result with parsed data
/// UrlValidationResult result = await SahihValidator.urlAsync("https://example.com");
/// if (result.isValid) {
///   print("Domain: ${result.data?.domain}");
/// }
/// ```
///
/// ### Complex Custom Validation
/// ```dart
/// String? productCodeError = SahihValidator.custom(
///   value: "PRD-1234",
///   title: "Product Code",
///   pattern: r'^PRD-\d{4}$',
///   customValidator: (value) {
///     if (value.startsWith("TEST")) {
///       return "Test product codes are not allowed in production";
///     }
///     return null;
///   },
/// );
/// ```
///
/// ## Best Practices
///
/// 1. **Use appropriate validation for context**: Use `loginPassword` for login forms
///    and `passwordParts` for registration forms.
///
/// 2. **Provide helpful error messages**: Customize error messages to guide users
///    on how to fix validation errors.
///
/// 3. **Handle existing values**: Always check against existing values to prevent
///    duplicates in your system.
///
/// 4. **Use trimWhitespace**: Keep the default `trimWhitespace: true` unless you
///    specifically need to preserve whitespace.
///
/// 5. **Combine validations**: Use multiple validation methods for complex forms
///    to ensure data quality.
///
/// For more examples, see the `/example` directory in this package.
library;

export 'Validator/validator_class.dart';
export 'Extensions/pattern_type.dart';
export 'Models/url_validate_result_model.dart';
