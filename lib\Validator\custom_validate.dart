import '../Extensions/pattern_type.dart';

/// Provides comprehensive custom validation with multiple configurable rules.
///
/// This function offers the most flexible validation capabilities, allowing you to
/// combine multiple validation rules for any type of input. It's ideal for validating
/// usernames, product codes, custom IDs, or any field with specific requirements.
///
/// Returns `null` if the value passes all validation rules, or an error message
/// describing the first validation failure encountered.
///
/// ## Validation Order
///
/// Validations are performed in this specific order:
/// 1. Required field check (if [isRequired] is true)
/// 2. Existing values check (if [existingValues] provided)
/// 3. Length validation ([minLength] and [maxLength])
/// 4. Character type restrictions ([allowOnlyNumbers], [allowOnlyLetters])
/// 5. Pattern matching ([pattern] or [patternType])
/// 6. Custom validation function ([customValidator])
///
/// ## Parameters
///
/// * [value]: The input value to validate (required)
/// * [title]: Field name used in default error messages. Default: "Field"
/// * [trimWhitespace]: Whether to trim whitespace before validation. Default: `true`
/// * [isRequired]: Whether the field is required. Default: `true`
/// * [minLength]: Minimum character length (optional)
/// * [maxLength]: Maximum character length (optional)
/// * [pattern]: Custom regex pattern string (optional)
/// * [patternType]: Predefined pattern from [PatternType] enum (optional)
/// * [allowOnlyNumbers]: If true, only numeric characters allowed. Default: `false`
/// * [allowOnlyLetters]: If true, only letter characters allowed. Default: `false`
/// * [customValidator]: Custom validation function returning null for valid or error message
/// * [existingValues]: List of values that already exist
/// * Various message parameters for customizing error messages
///
/// ## Example
///
/// ```dart
/// String? error = customValidate(
///   value: "USER123",
///   title: "User ID",
///   minLength: 5,
///   maxLength: 10,
///   pattern: r'^[A-Z0-9]+$',
///   existingValues: ["ADMIN", "ROOT"],
///   customValidator: (value) => value.startsWith("TEST") ? "Test IDs not allowed" : null,
/// );
/// ```
String? customValidate({
  required String value,
  String? title,
  bool trimWhitespace = true,
  bool isRequired = true,
  int? minLength,
  int? maxLength,
  String? pattern,
  PatternType? patternType,
  bool allowOnlyNumbers = false,
  bool allowOnlyLetters = false,
  String? emptyMessage,
  String? minLengthMessage,
  String? maxLengthMessage,
  String? invalidPatternMessage,
  String? invalidNumberMessage,
  String? invalidLettersMessage,
  String? Function(String value)? customValidator,
  List<String>? existingValues,
  String? alreadyExistsMessage,
}) {
  final String fieldName = title ?? "Field";
  final String input = trimWhitespace ? value.trim() : value;

  if (isRequired && input.isEmpty) {
    return emptyMessage ?? "Please enter $fieldName.";
  }

  if (!isRequired && input.isEmpty) {
    return null;
  }

  if (existingValues != null && existingValues.contains(input)) {
    return alreadyExistsMessage ?? "$fieldName already exists.";
  }

  if (minLength != null && input.length < minLength) {
    return minLengthMessage ??
        "$fieldName must be at least $minLength characters long.";
  }

  if (maxLength != null && input.length > maxLength) {
    return maxLengthMessage ??
        "$fieldName must be at most $maxLength characters long.";
  }

  if (allowOnlyNumbers && !RegExp(r'^\d+$').hasMatch(input)) {
    return invalidNumberMessage ?? "$fieldName must contain only numbers.";
  }

  if (allowOnlyLetters && !RegExp(r'^[a-zA-Z]+$').hasMatch(input)) {
    return invalidLettersMessage ?? "$fieldName must contain only letters.";
  }

  if (patternType != null && !patternType.isValid(input)) {
    return invalidPatternMessage ?? "$fieldName format is invalid.";
  }

  if (pattern != null && !RegExp(pattern).hasMatch(input)) {
    return invalidPatternMessage ?? "$fieldName format is invalid.";
  }

  if (customValidator != null) {
    return customValidator(input);
  }

  return null;
}
